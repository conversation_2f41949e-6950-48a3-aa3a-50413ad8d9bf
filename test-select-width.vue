<template>
  <div class="test-container">
    <h2>El-Select-V2 宽度自适应测试</h2>
    
    <div class="test-case">
      <h3>测试案例 1: 短选项</h3>
      <el-select-v2 
        v-model="value1" 
        :options="shortOptions" 
        placeholder="请选择"
        @visible-change="onVisibleChange"
        style="width: 200px;"
      />
      <p>选中值: {{ value1 }}</p>
    </div>

    <div class="test-case">
      <h3>测试案例 2: 长选项</h3>
      <el-select-v2 
        v-model="value2" 
        :options="longOptions" 
        placeholder="请选择"
        @visible-change="onVisibleChange"
        style="width: 200px;"
      />
      <p>选中值: {{ value2 }}</p>
    </div>

    <div class="test-case">
      <h3>测试案例 3: 混合长度选项</h3>
      <el-select-v2 
        v-model="value3" 
        :options="mixedOptions" 
        placeholder="请选择"
        @visible-change="onVisibleChange"
        style="width: 200px;"
      />
      <p>选中值: {{ value3 }}</p>
    </div>

    <div class="info">
      <h3>计算信息</h3>
      <p>计算的宽度: {{ calculatedWidth }}px</p>
      <p>最长选项: "{{ longestOption }}"</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'

const value1 = ref('')
const value2 = ref('')
const value3 = ref('')

const shortOptions = [
  { label: 'High', value: 'high' },
  { label: 'Medium', value: 'medium' },
  { label: 'Low', value: 'low' }
]

const longOptions = [
  { label: 'Very High Priority Level', value: 'very-high' },
  { label: 'High Priority Level', value: 'high' },
  { label: 'Medium Priority Level', value: 'medium' },
  { label: 'Low Priority Level', value: 'low' },
  { label: 'Very Low Priority Level', value: 'very-low' }
]

const mixedOptions = [
  { label: 'Critical', value: 'critical' },
  { label: 'High', value: 'high' },
  { label: 'Normal', value: 'normal' },
  { label: 'Low', value: 'low' },
  { label: 'Very Low Priority Level', value: 'very-low' }
]

const allOptions = computed(() => [...shortOptions, ...longOptions, ...mixedOptions])

const longestOption = computed(() => {
  return allOptions.value.reduce((longest, current) => 
    current.label.length > longest.length ? current.label : longest, ''
  )
})

const calculatedWidth = computed(() => {
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  if (!context) return 120
  
  context.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif'
  
  let maxWidth = 0
  allOptions.value.forEach(option => {
    const textWidth = context.measureText(option.label).width
    if (textWidth > maxWidth) {
      maxWidth = textWidth
    }
  })
  
  const extraWidth = 40 // 内边距 + 安全边距
  const calculatedWidth = Math.ceil(maxWidth + extraWidth)
  const minWidth = 120
  const maxWidthLimit = 400
  
  return Math.max(minWidth, Math.min(calculatedWidth, maxWidthLimit))
})

const onVisibleChange = (visible) => {
  if (visible) {
    setTimeout(() => {
      const dropdowns = document.querySelectorAll('.el-select-dropdown')
      dropdowns.forEach(dropdown => {
        if (dropdown.offsetParent !== null) {
          dropdown.style.width = `${calculatedWidth.value}px`
          dropdown.style.minWidth = `${calculatedWidth.value}px`
          console.log(`设置下拉框宽度为: ${calculatedWidth.value}px`)
        }
      })
    }, 50)
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-case {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.info {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #409eff;
  border-radius: 8px;
  background: #ecf5ff;
}

h2 {
  color: #409eff;
  text-align: center;
}

h3 {
  color: #606266;
  margin-bottom: 10px;
}

p {
  margin: 10px 0;
  color: #606266;
}
</style>

<style>
/* 全局样式确保下拉框宽度自适应 */
.el-select-dropdown {
  width: auto !important;
  min-width: 120px !important;
}

.el-select-dropdown__item {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: unset !important;
}

.el-select-dropdown__wrap,
.el-scrollbar__wrap,
.el-scrollbar__view {
  width: auto !important;
}
</style>
