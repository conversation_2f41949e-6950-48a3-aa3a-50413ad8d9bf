<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Priority Dropdown Width Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
            padding: 20px;
        }
        .test-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .dropdown-simulation {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: white;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 6px 0;
            margin-top: 10px;
            display: none;
        }
        .dropdown-item {
            padding: 0 20px;
            line-height: 34px;
            font-size: 14px;
            color: #606266;
            cursor: pointer;
            white-space: nowrap;
        }
        .dropdown-item:hover {
            background-color: #f5f7fa;
        }
        .show-dropdown {
            display: block !important;
        }
        .trigger-button {
            padding: 8px 16px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
        }
        .width-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Priority Dropdown Width Calculation Test</h1>
    
    <div class="test-container">
        <h3>Test Case 1: Short Options</h3>
        <button class="trigger-button" onclick="toggleDropdown('dropdown1')">Priority (Short) ▼</button>
        <div id="dropdown1" class="dropdown-simulation">
            <div class="dropdown-item">High</div>
            <div class="dropdown-item">Medium</div>
            <div class="dropdown-item">Low</div>
        </div>
        <div class="width-info" id="info1"></div>
    </div>

    <div class="test-container">
        <h3>Test Case 2: Long Options</h3>
        <button class="trigger-button" onclick="toggleDropdown('dropdown2')">Priority (Long) ▼</button>
        <div id="dropdown2" class="dropdown-simulation">
            <div class="dropdown-item">Very High Priority</div>
            <div class="dropdown-item">High Priority</div>
            <div class="dropdown-item">Medium Priority</div>
            <div class="dropdown-item">Low Priority</div>
            <div class="dropdown-item">Very Low Priority</div>
        </div>
        <div class="width-info" id="info2"></div>
    </div>

    <div class="test-container">
        <h3>Test Case 3: Mixed Length Options</h3>
        <button class="trigger-button" onclick="toggleDropdown('dropdown3')">Priority (Mixed) ▼</button>
        <div id="dropdown3" class="dropdown-simulation">
            <div class="dropdown-item">Critical</div>
            <div class="dropdown-item">High</div>
            <div class="dropdown-item">Normal</div>
            <div class="dropdown-item">Low</div>
            <div class="dropdown-item">Very Low Priority Level</div>
        </div>
        <div class="width-info" id="info3"></div>
    </div>

    <script>
        function calculateDropdownWidth(options) {
            // 创建一个临时的 canvas 元素来测量文本宽度
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            if (!context) return 120;
            
            // 设置字体样式，与 el-select-v2 的字体保持一致
            context.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif';
            
            // 计算所有选项中最长的文本宽度
            let maxWidth = 0;
            options.forEach(option => {
                const textWidth = context.measureText(option).width;
                if (textWidth > maxWidth) {
                    maxWidth = textWidth;
                }
            });
            
            // 添加额外的空间：左右内边距(24px) + 安全边距(16px)
            const extraWidth = 24 + 16;
            const calculatedWidth = Math.ceil(maxWidth + extraWidth);
            
            // 设置最小宽度和最大宽度
            const minWidth = 120;
            const maxWidthLimit = 400;
            
            return Math.max(minWidth, Math.min(calculatedWidth, maxWidthLimit));
        }

        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const isVisible = dropdown.classList.contains('show-dropdown');
            
            // 隐藏所有下拉框
            document.querySelectorAll('.dropdown-simulation').forEach(el => {
                el.classList.remove('show-dropdown');
            });
            
            if (!isVisible) {
                dropdown.classList.add('show-dropdown');
                
                // 获取选项文本
                const options = Array.from(dropdown.querySelectorAll('.dropdown-item')).map(item => item.textContent);
                
                // 计算宽度
                const calculatedWidth = calculateDropdownWidth(options);
                
                // 应用宽度
                dropdown.style.width = calculatedWidth + 'px';
                
                // 显示信息
                const infoId = 'info' + dropdownId.slice(-1);
                const infoEl = document.getElementById(infoId);
                infoEl.innerHTML = `
                    <strong>Options:</strong> ${options.join(', ')}<br>
                    <strong>Calculated Width:</strong> ${calculatedWidth}px<br>
                    <strong>Longest Option:</strong> "${options.reduce((a, b) => a.length > b.length ? a : b)}"
                `;
            }
        }

        // 点击其他地方关闭下拉框
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.test-container')) {
                document.querySelectorAll('.dropdown-simulation').forEach(el => {
                    el.classList.remove('show-dropdown');
                });
            }
        });
    </script>
</body>
</html>
